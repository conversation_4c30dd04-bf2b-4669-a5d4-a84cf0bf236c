package com.example.linkeye;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.widget.FrameLayout;
import android.widget.TextView;
import android.view.View;

import tv.danmaku.ijk.media.player.IMediaPlayer;
import tv.danmaku.ijk.media.player.IjkMediaPlayer;

import java.io.IOException;

public class PlayerTileView extends FrameLayout implements SurfaceHolder.Callback {
    private SurfaceView surfaceView;
    private SurfaceHolder surfaceHolder;
    private TextView titleView;
    private TextView muteBadge;
    private View focusBorder;

    private IjkMediaPlayer player;
    private CameraInfo camera;
    private boolean muted;

    public PlayerTileView(Context context) {
        super(context);
        init(context);
    }

    public PlayerTileView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public PlayerTileView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.player_tile, this, true);
        surfaceView = (SurfaceView) findViewById(R.id.surface_view);
        surfaceHolder = surfaceView.getHolder();
        surfaceHolder.addCallback(this);
        titleView = (TextView) findViewById(R.id.title);
        muteBadge = (TextView) findViewById(R.id.mute_badge);
        focusBorder = findViewById(R.id.focus_border);

        setFocusable(true);
        setOnFocusChangeListener((v, hasFocus) -> {
            if (focusBorder != null) {
                focusBorder.setBackgroundColor(hasFocus ? 0x99FFFF00 : 0x00000000);
            }
        });
    }

    public void assignCamera(CameraInfo info) {
        this.camera = info;
        updateTitle();
        if (this.camera == null) {
            release();
            return;
        }
        restartIfReady();
    }

    public CameraInfo getAssignedCamera() {
        return camera;
    }

    public void setMuted(boolean muted) {
        this.muted = muted;
        applyMute();
    }

    public boolean isMuted() {
        return muted;
    }

    private void updateTitle() {
        if (titleView != null) {
            titleView.setText(camera != null ? camera.name : "空");
        }
        if (muteBadge != null) {
            muteBadge.setVisibility(muted ? VISIBLE : GONE);
        }
    }

    private void applyMute() {
        if (player != null) {
            float vol = muted ? 0f : 1f;
            player.setVolume(vol, vol);
        }
        if (muteBadge != null) {
            muteBadge.setVisibility(muted ? VISIBLE : GONE);
        }
    }

    private void restartIfReady() {
        if (surfaceHolder != null && surfaceHolder.getSurface().isValid()) {
            prepareAndStart();
        }
    }

    private void prepareAndStart() {
        release();
        if (camera == null || camera.url == null || camera.url.isEmpty()) return;
        player = new IjkMediaPlayer();
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "rtsp_transport", "tcp");
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "start-on-prepared", 1);
        final int SDL_FCC_YV12 = 0x32315659;
        player.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "overlay-format", SDL_FCC_YV12);
        player.setDisplay(surfaceHolder);
        try {
            player.setDataSource(camera.url);
            player.setOnPreparedListener(new IjkMediaPlayer.OnPreparedListener() {
                @Override
                public void onPrepared(IMediaPlayer mp) {
                    applyMute();
                    mp.start();
                }
            });
            player.setOnErrorListener(new IjkMediaPlayer.OnErrorListener() {
                @Override
                public boolean onError(IMediaPlayer mp, int what, int extra) {
                    // swallow errors per tile
                    return true;
                }
            });
            player.prepareAsync();
        } catch (IOException e) {
            // ignore for tile
        }
    }

    public void release() {
        if (player != null) {
            try { player.stop(); } catch (Exception ignored) {}
            player.reset();
            player.release();
            player = null;
        }
    }

    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        restartIfReady();
    }

    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) { }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        release();
    }
}
